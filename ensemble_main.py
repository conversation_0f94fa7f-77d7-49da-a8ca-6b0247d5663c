import pandas as pd
import torch
from transformers import AutoTokenizer
from sklearn.metrics import f1_score, precision_score, recall_score
from tqdm import tqdm

from ensemble_model import PhoBERTModel, XLMRoBERTaModel, VotingEnsemble
from ensemble_processor import create_dual_dataloaders
from process import VietnameseTextPreprocessor, preprocess_and_tokenize, predict_and_evaluate, save_predictions

import os
import numpy as np
from EvaluationSystemByFile import convert_labels_to_dict, get_common_attributeEntities, evaluation_labels

# Paths and hyperparameters
TRAIN_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Train.csv'
VAL_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Dev.csv'
TEST_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Test.csv'
MAX_LENGTH = 256
BATCH_SIZE = 16  # Smaller batch size due to multiple models

# Initialize preprocessor
vn_preprocessor = VietnameseTextPreprocessor(extra_teencodes={
    'khách sạn': ['ks'], 'nhà hàng': ['nhahang'], 'nhân viên': ['nv'],
    # ... other teencodes
}, max_correction_length=MAX_LENGTH)

# Load and preprocess data
df_train = pd.read_csv(TRAIN_PATH, encoding='utf8')
df_val = pd.read_csv(VAL_PATH, encoding='utf8')
df_test = pd.read_csv(TEST_PATH, encoding='utf8')

# Preprocess text
df_train = preprocess_and_tokenize(df_train, "Review", vn_preprocessor, None, BATCH_SIZE, MAX_LENGTH)
df_val = preprocess_and_tokenize(df_val, "Review", vn_preprocessor, None, BATCH_SIZE, MAX_LENGTH)
df_test = preprocess_and_tokenize(df_test, "Review", vn_preprocessor, None, BATCH_SIZE, MAX_LENGTH)

# Create dataloaders with dual tokenization
train_loader, val_loader, test_loader = create_dual_dataloaders(
    df_train, df_val, df_test,
    batch_size=BATCH_SIZE,
    max_length=MAX_LENGTH
)

# Initialize device
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Initialize individual models
num_aspects = len(df_train.columns) - 1  # Excluding Review column
phobert_model = PhoBERTModel(
    pretrained_model='vinai/phobert-base',
    num_aspects=num_aspects,
    hidden_size=768,
    dropout=0.3
).to(device)

xlm_model = XLMRoBERTaModel(
    pretrained_model='xlm-roberta-base',
    num_aspects=num_aspects,
    hidden_size=768,
    dropout=0.3
).to(device)

# Create ensemble model
ensemble_model = VotingEnsemble(
    num_aspects=num_aspects,
    models=[phobert_model, xlm_model],
    weights=[0.6, 0.4]  # Giving more weight to PhoBERT as it's specialized for Vietnamese
).to(device)

# Define optimizer and loss function
optimizer = torch.optim.AdamW(ensemble_model.parameters(), lr=2e-5)
criterion = torch.nn.CrossEntropyLoss()

# Training function
def train_ensemble(model, train_loader, val_loader, optimizer, criterion, device, num_epochs=5):
    best_val_loss = float('inf')
    best_model_state = None
    
    for epoch in range(num_epochs):
        print(f'\nEpoch {epoch + 1}/{num_epochs}')
        print('-' * 20)
        
        # Training phase
        model.train()
        total_loss = 0
        correct_predictions = 0
        total_predictions = 0
        
        progress_bar = tqdm(train_loader, desc=f'Training')
        for batch in progress_bar:
            # Move batch to device
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
            labels = batch['labels']
            
            # Forward pass
            optimizer.zero_grad()
            outputs = model(batch)
            
            # Calculate loss
            batch_size, num_aspects, num_classes = outputs.shape
            outputs_reshaped = outputs.view(-1, num_classes)
            labels_reshaped = labels.view(-1, num_classes)
            loss = criterion(outputs_reshaped, labels_reshaped)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            # Calculate accuracy
            predictions = torch.argmax(outputs_reshaped, dim=1)
            true_labels = torch.argmax(labels_reshaped, dim=1)
            correct_predictions += (predictions == true_labels).sum().item()
            total_predictions += predictions.shape[0]
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{correct_predictions/total_predictions:.4f}'
            })
        
        avg_train_loss = total_loss / len(train_loader)
        train_accuracy = correct_predictions / total_predictions
        print(f'Training Loss: {avg_train_loss:.4f}, Accuracy: {train_accuracy:.4f}')
        
        # Validation phase
        model.eval()
        val_loss = 0
        correct_predictions = 0
        total_predictions = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc='Validating'):
                batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
                labels = batch['labels']
                
                outputs = model(batch)
                
                batch_size, num_aspects, num_classes = outputs.shape
                outputs_reshaped = outputs.view(-1, num_classes)
                labels_reshaped = labels.view(-1, num_classes)
                loss = criterion(outputs_reshaped, labels_reshaped)
                
                val_loss += loss.item()
                
                predictions = torch.argmax(outputs_reshaped, dim=1)
                true_labels = torch.argmax(labels_reshaped, dim=1)
                correct_predictions += (predictions == true_labels).sum().item()
                total_predictions += predictions.shape[0]
        
        avg_val_loss = val_loss / len(val_loader)
        val_accuracy = correct_predictions / total_predictions
        print(f'Validation Loss: {avg_val_loss:.4f}, Accuracy: {val_accuracy:.4f}')
        
        # Save best model
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            best_model_state = model.state_dict()
            print('Best model saved!')
    
    return best_model_state, best_val_loss

# Train the ensemble model
best_model_state, best_val_loss = train_ensemble(
    model=ensemble_model,
    train_loader=train_loader,
    val_loader=val_loader,
    optimizer=optimizer,
    criterion=criterion,
    device=device,
    num_epochs=5
)

# Save best model
torch.save(best_model_state, 'best_ensemble_model.pth')
print(f'Best validation loss: {best_val_loss:.4f}')

# Load best model for evaluation
ensemble_model.load_state_dict(best_model_state)

# Generate prediction file
test_output_path = "predictions/test_predictions.txt"
gold_output_path = "predictions/gold_labels.txt"

# Get aspect columns
aspect_columns = [col for col in df_test.columns if col != 'Review']

# Generate predictions from the model
def generate_prediction_file(model, data_loader, device, output_path, aspect_columns):
    model.eval()
    all_predictions = []
    all_texts = []
    
    # Mapping from index to sentiment label
    idx_to_sentiment = {0: 'None', 1: 'positive', 2: 'negative', 3: 'neutral'}
    
    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Generating predictions"):
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
            
            # Get review texts
            texts = batch.get('review_text', [])
            all_texts.extend(texts)
            
            # Forward pass
            outputs = model(batch)
            
            # Get predictions
            predictions = torch.argmax(outputs, dim=-1).cpu().numpy()
            all_predictions.append(predictions)
    
    # Combine all predictions
    all_predictions = np.vstack(all_predictions)
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Write predictions to file in the format expected by EvaluationSystemByFile
    with open(output_path, 'w', encoding='utf-8') as f:
        for i in range(len(all_texts)):
            f.write(f"#{i+1}\n")
            f.write(f"{all_texts[i]}\n")
            
            # Create aspect-sentiment pairs
            aspects = []
            for j, aspect in enumerate(aspect_columns):
                sentiment_idx = all_predictions[i][j]
                if sentiment_idx != 0:  # Skip 'None' sentiment
                    aspects.append(f"{{{aspect}, {idx_to_sentiment[sentiment_idx]}}}")
            
            # Write aspects line
            if aspects:
                f.write(", ".join(aspects) + "\n\n")
            else:
                f.write("\n\n")
    
    return output_path

# Generate gold labels file
def generate_gold_file(data_loader, output_path, aspect_columns):
    all_texts = []
    all_labels = []
    
    # Mapping from one-hot index to sentiment label
    idx_to_sentiment = {0: 'None', 1: 'positive', 2: 'negative', 3: 'neutral'}
    
    for batch in tqdm(data_loader, desc="Generating gold labels"):
        texts = batch.get('review_text', [])
        labels = batch['labels'].cpu().numpy()
        
        all_texts.extend(texts)
        all_labels.append(labels)
    
    # Combine all labels
    all_labels = np.vstack(all_labels)
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Write gold labels to file
    with open(output_path, 'w', encoding='utf-8') as f:
        for i in range(len(all_texts)):
            f.write(f"#{i+1}\n")
            f.write(f"{all_texts[i]}\n")
            
            # Create aspect-sentiment pairs
            aspects = []
            for j, aspect in enumerate(aspect_columns):
                sentiment_idx = np.argmax(all_labels[i][j])
                if sentiment_idx != 0:  # Skip 'None' sentiment
                    aspects.append(f"{{{aspect}, {idx_to_sentiment[sentiment_idx]}}}")
            
            # Write aspects line
            if aspects:
                f.write(", ".join(aspects) + "\n\n")
            else:
                f.write("\n\n")
    
    return output_path

# Generate prediction and gold files
predict_file = generate_prediction_file(
    model=ensemble_model,
    data_loader=test_loader,
    device=device,
    output_path=test_output_path,
    aspect_columns=aspect_columns
)

gold_file = generate_gold_file(
    data_loader=test_loader,
    output_path=gold_output_path,
    aspect_columns=aspect_columns
)

# Use EvaluationSystemByFile to evaluate
print("\nEvaluating using EvaluationSystemByFile:")
print("-" * 50)
from EvaluationSystemByFile import evaluation_system_by_file
evaluation_system_by_file(gold_file, predict_file)
