import torch
from transformers import AutoModel, AutoTokenizer
from torch.utils.data import DataLoader

class PhoBERTModel(torch.nn.Module):
    def __init__(self, pretrained_model='vinai/phobert-base', num_aspects=4, hidden_size=768, dropout=0.3):
        super(PhoBERTModel, self).__init__()
        self.encoder = AutoModel.from_pretrained(pretrained_model)
        self.classifier = torch.nn.ModuleList([
            torch.nn.Sequential(
                torch.nn.Linear(hidden_size, hidden_size),
                torch.nn.ReLU(),
                torch.nn.Dropout(dropout),
                torch.nn.Linear(hidden_size, 4)  # 4 classes: None, Positive, Negative, Neutral
            ) for _ in range(num_aspects)
        ])
        
    def forward(self, input_ids, attention_mask, token_type_ids):
        outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask, token_type_ids=token_type_ids)
        pooled_output = outputs.last_hidden_state[:, 0]  # [CLS] token
        
        sentiment_logits = []
        for aspect_classifier in self.classifier:
            sentiment_logits.append(aspect_classifier(pooled_output))
            
        return torch.stack(sentiment_logits, dim=1)  # [batch_size, num_aspects, 4]

class XLMRoBERTaModel(torch.nn.Module):
    def __init__(self, pretrained_model='xlm-roberta-base', num_aspects=4, hidden_size=768, dropout=0.3):
        super(XLMRoBERTaModel, self).__init__()
        self.encoder = AutoModel.from_pretrained(pretrained_model)
        self.classifier = torch.nn.ModuleList([
            torch.nn.Sequential(
                torch.nn.Linear(hidden_size, hidden_size),
                torch.nn.ReLU(),
                torch.nn.Dropout(dropout),
                torch.nn.Linear(hidden_size, 4)  # 4 classes: None, Positive, Negative, Neutral
            ) for _ in range(num_aspects)
        ])
        
    def forward(self, input_ids, attention_mask):
        outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.last_hidden_state[:, 0]  # [CLS] token
        
        sentiment_logits = []
        for aspect_classifier in self.classifier:
            sentiment_logits.append(aspect_classifier(pooled_output))
            
        return torch.stack(sentiment_logits, dim=1)  # [batch_size, num_aspects, 4]

class VotingEnsemble(torch.nn.Module):
    def __init__(self, num_aspects=4, models=None, weights=None):
        super(VotingEnsemble, self).__init__()
        self.models = torch.nn.ModuleList(models) if models else torch.nn.ModuleList()
        self.weights = weights if weights else [1.0] * len(self.models)
        self.num_aspects = num_aspects
        
    def forward(self, batch):
        all_logits = []
        
        # Get predictions from PhoBERT model
        if isinstance(self.models[0], PhoBERTModel):
            phobert_logits = self.models[0](
                input_ids=batch['phobert_input_ids'],
                attention_mask=batch['phobert_attention_mask'],
                token_type_ids=batch['phobert_token_type_ids']
            )
            all_logits.append(phobert_logits * self.weights[0])
        
        # Get predictions from XLM-RoBERTa model
        if isinstance(self.models[1], XLMRoBERTaModel):
            xlm_logits = self.models[1](
                input_ids=batch['xlm_input_ids'],
                attention_mask=batch['xlm_attention_mask']
            )
            all_logits.append(xlm_logits * self.weights[1])
        
        # Combine predictions using weighted average
        ensemble_logits = sum(all_logits) / sum(self.weights)
        return ensemble_logits