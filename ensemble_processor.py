import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer

class DualTokenizerDataset(Dataset):
    def __init__(self, df, phobert_tokenizer, xlm_tokenizer, max_length=256):
        self.df = df
        self.phobert_tokenizer = phobert_tokenizer
        self.xlm_tokenizer = xlm_tokenizer
        self.max_length = max_length
        
        # Get aspect columns (excluding Review column)
        self.aspect_columns = [col for col in df.columns if col != 'Review']
        self.num_aspects = len(self.aspect_columns)
        
        # Map sentiment values to one-hot vectors
        self.sentiment_map = {
            0: [1, 0, 0, 0],  # None
            1: [0, 1, 0, 0],  # Positive
            2: [0, 0, 1, 0],  # Negative
            3: [0, 0, 0, 1]   # Neutral
        }

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]
        text = row['Review']
        
        # Tokenize with PhoBERT
        phobert_encoding = self.phobert_tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Tokenize with XLM-RoBERTa
        xlm_encoding = self.xlm_tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Create labels tensor [num_aspects, 4] for sentiment classes
        labels = torch.zeros((self.num_aspects, 4))
        for i, aspect in enumerate(self.aspect_columns):
            sentiment_value = row[aspect]
            labels[i] = torch.tensor(self.sentiment_map[sentiment_value])
        
        return {
            'phobert_input_ids': phobert_encoding['input_ids'].squeeze(0),
            'phobert_attention_mask': phobert_encoding['attention_mask'].squeeze(0),
            'phobert_token_type_ids': phobert_encoding['token_type_ids'].squeeze(0),
            'xlm_input_ids': xlm_encoding['input_ids'].squeeze(0),
            'xlm_attention_mask': xlm_encoding['attention_mask'].squeeze(0),
            'labels': labels,
            'review_text': text
        }

def create_dual_dataloaders(train_df, val_df, test_df, batch_size=32, max_length=256):
    # Initialize tokenizers
    phobert_tokenizer = AutoTokenizer.from_pretrained('vinai/phobert-base')
    xlm_tokenizer = AutoTokenizer.from_pretrained('xlm-roberta-base')
    
    # Create datasets
    train_dataset = DualTokenizerDataset(train_df, phobert_tokenizer, xlm_tokenizer, max_length)
    val_dataset = DualTokenizerDataset(val_df, phobert_tokenizer, xlm_tokenizer, max_length)
    test_dataset = DualTokenizerDataset(test_df, phobert_tokenizer, xlm_tokenizer, max_length)
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)
    
    return train_loader, val_loader, test_loader