import pandas as pd
import numpy as np
import transformers
from transformers import AutoTokenizer, AutoModel, TFAutoModel

import torch
import matplotlib.pyplot as plt

from tqdm import tqdm
import re
import os
import csv

from process import VietnameseTextPreprocessor, preprocess_and_tokenize, create_dataloaders, train_model, evaluate, predict_and_evaluate, predict_aspects_and_sentiments, save_predictions
from model_v1 import PhoBERTBiLSTM
from model_v2 import PhoBERTBiLSTMV2

TRAIN_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Train.csv'
VAL_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Dev.csv'
TEST_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Test.csv'
MAX_LENGTH = 256
BATCH_SIZE = 32

vn_preprocessor = VietnameseTextPreprocessor(extra_teencodes={
    'khách sạn': ['ks'], 'nhà hàng': ['nhahang'], 'nhân viên': ['nv'],
    'cửa hàng': ['store', 'sop', 'shopE', 'shop'],
    'sản phẩm': ['sp', 'product'], 'hàng': ['hàg'],
    'giao hàng': ['ship', 'delivery', 'síp'], 'đặt hàng': ['order'],
    'chuẩn chính hãng': ['authentic', 'aut', 'auth'], 'hạn sử dụng': ['date', 'hsd'],
    'điện thoại': ['dt'],  'facebook': ['fb', 'face'],
    'nhắn tin': ['nt', 'ib'], 'trả lời': ['tl', 'trl', 'rep'],
    'feedback': ['fback', 'fedback'], 'sử dụng': ['sd'], 'xài': ['sài'],
}, max_correction_length=MAX_LENGTH)

from transformers import AutoTokenizer
tokenizer = AutoTokenizer.from_pretrained('vinai/phobert-base')

df_train = pd.read_csv(TRAIN_PATH, encoding='utf8')
df_val = pd.read_csv(VAL_PATH, encoding='utf8')
df_test = pd.read_csv(TEST_PATH, encoding='utf8')

df_train = preprocess_and_tokenize(df_train, "Review", vn_preprocessor, tokenizer, BATCH_SIZE * 2, MAX_LENGTH)
df_test = preprocess_and_tokenize(df_test, "Review", vn_preprocessor, tokenizer, BATCH_SIZE * 2, MAX_LENGTH)
df_val = preprocess_and_tokenize(df_val, "Review", vn_preprocessor, tokenizer, BATCH_SIZE * 2, MAX_LENGTH)

df_train.drop(columns="input_ids", inplace=True)
df_test.drop(columns="input_ids", inplace=True)
df_val.drop(columns="input_ids", inplace=True)

# Create dataloaders
train_loader, val_loader, test_loader = create_dataloaders(
    df_train, df_val, df_test,
    batch_size=BATCH_SIZE,
    max_length=MAX_LENGTH
)

# Initialize model, optimizer, and criterion
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = PhoBERTBiLSTM(
    pretrained_model='vinai/phobert-base',
    num_aspects=len(df_train.columns) - 1,  # Trừ đi cột Review
    hidden_size=768,
    num_layers=2,
    dropout=0.3
).to(device)

optimizer = torch.optim.AdamW(model.parameters(), lr=2e-5)
criterion = torch.nn.CrossEntropyLoss()

# Train model
best_model_state, best_val_loss = train_model(
    model=model,
    train_loader=train_loader,
    val_loader=val_loader,
    optimizer=optimizer,
    criterion=criterion,
    device=device,
    num_epochs=5
)

# Save best model
torch.save(best_model_state, 'best_model.pth')
print(f'Best validation loss: {best_val_loss:.4f}')

# Load best model for evaluation or inference
model.load_state_dict(best_model_state)
test_loss, test_acc = evaluate(model, test_loader, criterion, device)
print(f'\nTest Results:')
print(f'Loss: {test_loss:.4f}, Accuracy: {test_acc:.4f}')

# 1. Predict và tính metrics
predictions, metrics = predict_and_evaluate(model, test_loader, device)

# 2. Generate detailed predictions for each review
predictions_df = predict_aspects_and_sentiments(
    model=model,
    test_loader=test_loader,
    device=device,
    aspect_columns=[col for col in df_test.columns if col != 'Review']
)

# 3. Save results
save_predictions(
    predictions_df=predictions_df,
    metrics=metrics,
    output_path='results',
    save_csv=True,
    save_metrics=True
)

# 4. Print summary metrics
print("\nTest Set Metrics:")
print("-" * 50)
print(f"Macro F1-Score: {metrics['f1_macro']:.4f}")
print(f"Micro F1-Score: {metrics['f1_micro']:.4f}")
print(f"Weighted F1-Score: {metrics['f1_weighted']:.4f}")
print("\nPrecision Scores:")
print(f"Macro: {metrics['precision_macro']:.4f}")
print(f"Micro: {metrics['precision_micro']:.4f}")
print(f"Weighted: {metrics['precision_weighted']:.4f}")
print("\nRecall Scores:")
print(f"Macro: {metrics['recall_macro']:.4f}")
print(f"Micro: {metrics['recall_micro']:.4f}")
print(f"Weighted: {metrics['recall_weighted']:.4f}")
