import os
import pandas as pd
import numpy as np

import transformers
from transformers import AutoTokenizer, AutoModel, TFAutoModel

import torch
import matplotlib.pyplot as plt

from tqdm import tqdm
import re
import os
import csv
import pandas as pd
import unicodedata  # Đả<PERSON> bảo đã import unicodedata
from unidecode import unidecode  # Thêm import này

def sanitize_layer_name(name):
    # Sử dụng unidecode để chuyển các ký tự Unicode sang ASCII
    name = unidecode(name)
    # Thay thế bất kỳ ký tự nào không phải là chữ, số, dấu gạch dưới, dấu chấm hoặc dấu gạch ngang bằng dấu gạch dưới
    name = re.sub(r'[^A-Za-z0-9_.\-]', '_', name)
    # Đ<PERSON><PERSON> bảo tên bắt đầu bằng ký tự hợp lệ
    if not re.match(r'^[A-Za-z0-9.]', name):
        name = 'layer_' + name
    return name

class PolarityMapping:
    INDEX_TO_POLARITY = { 0: None, 1: 'positive', 2: 'negative', 3: 'neutral' }
    INDEX_TO_ONEHOT = { 0: [1, 0, 0, 0], 1: [0, 1, 0, 0], 2: [0, 0, 1, 0], 3: [0, 0, 0, 1] }
    POLARITY_TO_INDEX = { None: 0, 'positive': 1, 'negative': 2, 'neutral': 3 }

class ParserData:
    def __init__(self, train_txt_path, val_txt_path=None, test_txt_path=None):
        self.dataset_paths = { 'train': train_txt_path, 'val': val_txt_path, 'test': test_txt_path }
        self.reviews = { 'train': [], 'val': [], 'test': [] }
        self.aspect_categories = set()
        
        # Loại bỏ các tập dữ liệu không có đường dẫn
        for dataset_type, txt_path in list(self.dataset_paths.items()):
            if not txt_path: 
                self.dataset_paths.pop(dataset_type)
                self.reviews.pop(dataset_type)
        self._parse_input_files()

    def _normalize_sentiment_data(self, line):
        """
        Chuẩn hóa dòng dữ liệu để sửa lỗi phổ biến.
        """
        # Sửa lỗi chính tả phổ biến trong sentiment
        line = re.sub(r'negav[^\s,}]*', 'negative', line, flags=re.IGNORECASE)
        line = re.sub(r'pos[^\s,}]*', 'positive', line, flags=re.IGNORECASE)
        line = re.sub(r'neut[^\s,}]*', 'neutral', line, flags=re.IGNORECASE)
    
        # Loại bỏ khoảng trắng không cần thiết
        line = re.sub(r'\s+', ' ', line)
    
        return line

    def _parse_input_files(self):
        print(f'[INFO] Parsing {len(self.dataset_paths)} input files...')
        for dataset_type, txt_path in self.dataset_paths.items():
            with open(txt_path, 'r', encoding='utf-8') as txt_file:
                content = txt_file.read()
                review_blocks = content.strip().split('\n\n')
                
                for block in tqdm(review_blocks, desc=f'Parsing {dataset_type}'):
                    lines = block.split('\n')

                    if len(lines) < 3:
                        # Bỏ qua các block không hợp lệ
                        continue

                    # Chuẩn hóa dòng chứa thông tin sentiment
                    normalized_line = self._normalize_sentiment_data(lines[2].strip())
                    
                    sentiment_info = re.findall(r'\{([^#{},]+)(?:#([^,}]+))?, ([^}]+)\}', normalized_line)

                    review_data = {}
                    for aspect, category, polarity in sentiment_info:
                        aspect_category = f'{aspect.strip()}#{category.strip()}' if category else aspect.strip()
                        sanitized_aspect_category = sanitize_layer_name(aspect_category)  # Chuẩn hóa tên khía cạnh
                        self.aspect_categories.add(aspect_category)
                        polarity_index = PolarityMapping.POLARITY_TO_INDEX.get(polarity.strip().lower(), 0)  # Mặc định None nếu lỗi
                        review_data[aspect_category] = polarity_index
                    
                    self.reviews[dataset_type].append((lines[1].strip(), review_data))
        self.aspect_categories = sorted(self.aspect_categories)

    def txt2csv(self):
        print('[INFO] Converting parsed data to CSV files...')
        for dataset, txt_path in self.dataset_paths.items():
            csv_path = txt_path.replace('.txt', '.csv').replace('input', 'working')

            # Kiểm tra và tạo thư mục nếu cần
            csv_dir = os.path.dirname(csv_path)
            if not os.path.exists(csv_dir):
                os.makedirs(csv_dir)
                
            with open(csv_path, 'w', newline='', encoding='utf-8') as csv_file:
                writer = csv.writer(csv_file)
                writer.writerow(['Review'] + self.aspect_categories)

                for review_text, review_data in tqdm(self.reviews[dataset], desc=f'Writing {dataset} CSV'):
                    row = [review_text] + [review_data.get(aspect_category, 0) for aspect_category in self.aspect_categories]
                    writer.writerow(row)
        
        # Sau khi chuyển đổi TXT sang CSV, thực hiện bước lọc cột ít giá trị
        # self._remove_low_frequency_columns()

    def _remove_low_frequency_columns(self, threshold=5):
        """
        Loại bỏ các cột ít giá trị từ các file CSV đã tạo.
        """
        print('[INFO] Removing low-frequency columns from CSV files...')
        for dataset, txt_path in self.dataset_paths.items():
            csv_path = txt_path.replace('.txt', '.csv').replace('input', 'working')
            # filtered_csv_path = csv_path.replace('.csv', '_filtered.csv')

            # Đọc CSV thành DataFrame
            df = pd.read_csv(csv_path)

            # Tính tổng các giá trị trong mỗi cột (bỏ cột 'Review')
            aspect_counts = df.iloc[:, 1:].sum()

            # Xác định các cột có tổng nhỏ hơn ngưỡng
            low_frequency_columns = aspect_counts[aspect_counts < threshold].index

            # Loại bỏ các cột ít giá trị
            df_filtered = df.drop(columns=low_frequency_columns)

            # Lưu DataFrame đã được lọc lại thành file CSV mới
            df_filtered.to_csv(csv_path, index=False, encoding='utf-8')

            print(f'[INFO] Saved filtered CSV: {csv_path}')

    @staticmethod
    def save_as(save_path, raw_texts, encoded_review_labels, aspect_category_names):
        with open(save_path, 'w', encoding='utf-8') as file:
            for index, encoded_label in tqdm(enumerate(encoded_review_labels), desc='Saving as TXT'):
                polarities = map(lambda x: PolarityMapping.INDEX_TO_POLARITY[x], encoded_label)
                acsa = ', '.join(
                    f'{{{aspect_category}, {polarity}}}' 
                    for aspect_category, polarity in zip(aspect_category_names, polarities) if polarity
                )
                file.write(f"#{index + 1}\n{raw_texts[index]}\n{acsa}\n\n")

import os
import emoji
import urllib
import requests
import regex as re

from io import StringIO
from underthesea import word_tokenize
from transformers import pipeline


class VietnameseTextCleaner: # https://ihateregex.io
    VN_CHARS = 'áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệóòỏõọôốồổỗộơớờởỡợíìỉĩịúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÍÌỈĨỊÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ'
    
    @staticmethod
    def remove_html(text):
        return re.sub(r'<[^>]*>', '', text)
    
    @staticmethod
    def remove_emoji(text):
        return emoji.replace_emoji(text, '')
    
    @staticmethod
    def remove_url(text):
        return re.sub(r'https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()!@:%_\+.~#?&\/\/=]*)', '', text)
    
    @staticmethod
    def remove_email(text):
        return re.sub(r'[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+', '', text)
    
    @staticmethod
    def remove_phone_number(text):
        return re.sub(r'^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$', '', text)
    
    @staticmethod
    def remove_hashtags(text):
        return re.sub(r'#\w+', '', text)
    
    @staticmethod
    def remove_unnecessary_characters(text):
        text = re.sub(fr"[^\sa-zA-Z0-9{VietnameseTextCleaner.VN_CHARS}]", ' ', text)
        return re.sub(r'\s+', ' ', text).strip() # Remove extra whitespace
    
    @staticmethod
    def process_text(text):
        text = VietnameseTextCleaner.remove_html(text)
        text = VietnameseTextCleaner.remove_emoji(text)
        text = VietnameseTextCleaner.remove_url(text)
        text = VietnameseTextCleaner.remove_email(text)
        text = VietnameseTextCleaner.remove_phone_number(text)
        text = VietnameseTextCleaner.remove_hashtags(text)
        return VietnameseTextCleaner.remove_unnecessary_characters(text)


class VietnameseToneNormalizer:
    VOWELS_TABLE = [
        ['a', 'à', 'á', 'ả', 'ã', 'ạ', 'a'],
        ['ă', 'ằ', 'ắ', 'ẳ', 'ẵ', 'ặ', 'aw'],
        ['â', 'ầ', 'ấ', 'ẩ', 'ẫ', 'ậ', 'aa'],
        ['e', 'è', 'é', 'ẻ', 'ẽ', 'ẹ', 'e' ],
        ['ê', 'ề', 'ế', 'ể', 'ễ', 'ệ', 'ee'],
        ['i', 'ì', 'í', 'ỉ', 'ĩ', 'ị', 'i' ],
        ['o', 'ò', 'ó', 'ỏ', 'õ', 'ọ', 'o' ],
        ['ô', 'ồ', 'ố', 'ổ', 'ỗ', 'ộ', 'oo'],
        ['ơ', 'ờ', 'ớ', 'ở', 'ỡ', 'ợ', 'ow'],
        ['u', 'ù', 'ú', 'ủ', 'ũ', 'ụ', 'u' ],
        ['ư', 'ừ', 'ứ', 'ử', 'ữ', 'ự', 'uw'],
        ['y', 'ỳ', 'ý', 'ỷ', 'ỹ', 'ỵ', 'y']
    ]
    
    # VOWELS_TO_IDS = {}
    # for i, row in enumerate(VOWELS_TABLE):
    #     for j, char in enumerate(row[:-1]):
    #         VOWELS_TO_IDS[char] = (i, j)
    VOWELS_TO_IDS = {
        'a': (0, 0), 'à': (0, 1), 'á': (0, 2), 'ả': (0, 3), 'ã': (0, 4), 'ạ': (0, 5), 
        'ă': (1, 0), 'ằ': (1, 1), 'ắ': (1, 2), 'ẳ': (1, 3), 'ẵ': (1, 4), 'ặ': (1, 5), 
        'â': (2, 0), 'ầ': (2, 1), 'ấ': (2, 2), 'ẩ': (2, 3), 'ẫ': (2, 4), 'ậ': (2, 5), 
        'e': (3, 0), 'è': (3, 1), 'é': (3, 2), 'ẻ': (3, 3), 'ẽ': (3, 4), 'ẹ': (3, 5), 
        'ê': (4, 0), 'ề': (4, 1), 'ế': (4, 2), 'ể': (4, 3), 'ễ': (4, 4), 'ệ': (4, 5), 
        'i': (5, 0), 'ì': (5, 1), 'í': (5, 2), 'ỉ': (5, 3), 'ĩ': (5, 4), 'ị': (5, 5), 
        'o': (6, 0), 'ò': (6, 1), 'ó': (6, 2), 'ỏ': (6, 3), 'õ': (6, 4), 'ọ': (6, 5), 
        'ô': (7, 0), 'ồ': (7, 1), 'ố': (7, 2), 'ổ': (7, 3), 'ỗ': (7, 4), 'ộ': (7, 5), 
        'ơ': (8, 0), 'ờ': (8, 1), 'ớ': (8, 2), 'ở': (8, 3), 'ỡ': (8, 4), 'ợ': (8, 5), 
        'u': (9, 0), 'ù': (9, 1), 'ú': (9, 2), 'ủ': (9, 3), 'ũ': (9, 4), 'ụ': (9, 5), 
        'ư': (10, 0), 'ừ': (10, 1), 'ứ': (10, 2), 'ử': (10, 3), 'ữ': (10, 4), 'ự': (10, 5), 
        'y': (11, 0), 'ỳ': (11, 1), 'ý': (11, 2), 'ỷ': (11, 3), 'ỹ': (11, 4), 'ỵ': (11, 5)
    }
    
    VINAI_NORMALIZED_TONE = {
        'òa': 'oà', 'Òa': 'Oà', 'ÒA': 'OÀ', 
        'óa': 'oá', 'Óa': 'Oá', 'ÓA': 'OÁ', 
        'ỏa': 'oả', 'Ỏa': 'Oả', 'ỎA': 'OẢ',
        'õa': 'oã', 'Õa': 'Oã', 'ÕA': 'OÃ',
        'ọa': 'oạ', 'Ọa': 'Oạ', 'ỌA': 'OẠ',
        'òe': 'oè', 'Òe': 'Oè', 'ÒE': 'OÈ',
        'óe': 'oé', 'Óe': 'Oé', 'ÓE': 'OÉ',
        'ỏe': 'oẻ', 'Ỏe': 'Oẻ', 'ỎE': 'OẺ',
        'õe': 'oẽ', 'Õe': 'Oẽ', 'ÕE': 'OẼ',
        'ọe': 'oẹ', 'Ọe': 'Oẹ', 'ỌE': 'OẸ',
        'ùy': 'uỳ', 'Ùy': 'Uỳ', 'ÙY': 'UỲ',
        'úy': 'uý', 'Úy': 'Uý', 'ÚY': 'UÝ',
        'ủy': 'uỷ', 'Ủy': 'Uỷ', 'ỦY': 'UỶ',
        'ũy': 'uỹ', 'Ũy': 'Uỹ', 'ŨY': 'UỸ',
        'ụy': 'uỵ', 'Ụy': 'Uỵ', 'ỤY': 'UỴ',
    }


    @staticmethod
    def normalize_unicode(text):
        char1252 = r'à|á|ả|ã|ạ|ầ|ấ|ẩ|ẫ|ậ|ằ|ắ|ẳ|ẵ|ặ|è|é|ẻ|ẽ|ẹ|ề|ế|ể|ễ|ệ|ì|í|ỉ|ĩ|ị|ò|ó|ỏ|õ|ọ|ồ|ố|ổ|ỗ|ộ|ờ|ớ|ở|ỡ|ợ|ù|ú|ủ|ũ|ụ|ừ|ứ|ử|ữ|ự|ỳ|ý|ỷ|ỹ|ỵ|À|Á|Ả|Ã|Ạ|Ầ|Ấ|Ẩ|Ẫ|Ậ|Ằ|Ắ|Ẳ|Ẵ|Ặ|È|É|Ẻ|Ẽ|Ẹ|Ề|Ế|Ể|Ễ|Ệ|Ì|Í|Ỉ|Ĩ|Ị|Ò|Ó|Ỏ|Õ|Ọ|Ồ|Ố|Ổ|Ỗ|Ộ|Ờ|Ớ|Ở|Ỡ|Ợ|Ù|Ú|Ủ|Ũ|Ụ|Ừ|Ứ|Ử|Ữ|Ự|Ỳ|Ý|Ỷ|Ỹ|Ỵ'
        charutf8 = r'à|á|ả|ã|ạ|ầ|ấ|ẩ|ẫ|ậ|ằ|ắ|ẳ|ẵ|ặ|è|é|ẻ|ẽ|ẹ|ề|ế|ể|ễ|ệ|ì|í|ỉ|ĩ|ị|ò|ó|ỏ|õ|ọ|ồ|ố|ổ|ỗ|ộ|ờ|ớ|ở|ỡ|ợ|ù|ú|ủ|ũ|ụ|ừ|ứ|ử|ữ|ự|ỳ|ý|ỷ|ỹ|ỵ|À|Á|Ả|Ã|Ạ|Ầ|Ấ|Ẩ|Ẫ|Ậ|Ằ|Ắ|Ẳ|Ẵ|Ặ|È|É|Ẻ|Ẽ|Ẹ|Ề|Ế|Ể|Ễ|Ệ|Ì|Í|Ỉ|Ĩ|Ị|Ò|Ó|Ỏ|Õ|Ọ|Ồ|Ố|Ổ|Ỗ|Ộ|Ờ|Ớ|Ở|Ỡ|Ợ|Ù|Ú|Ủ|Ũ|Ụ|Ừ|Ứ|Ử|Ữ|Ự|Ỳ|Ý|Ỷ|Ỹ|Ỵ'
        char_map = dict(zip(char1252.split('|'), charutf8.split('|')))
        return re.sub(char1252, lambda x: char_map[x.group()], text.strip())
    
    
    @staticmethod
    def normalize_sentence_typing(text, vinai_normalization=False):
        # https://github.com/VinAIResearch/BARTpho/blob/main/VietnameseToneNormalization.md
        if vinai_normalization: # Just simply replace the wrong tone with the correct one defined by VinAI
            for wrong, correct in VietnameseToneNormalizer.VINAI_NORMALIZED_TONE.items():
                text = text.replace(wrong, correct)
            return text.strip()
        
        # Or you can use this algorithm developed by Behitek to normalize Vietnamese typing in a sentence 
        words = text.strip().split()
        for index, word in enumerate(words):
            cw = re.sub(r'(^\p{P}*)([p{L}.]*\p{L}+)(\p{P}*$)', r'\1/\2/\3', word).split('/')
            if len(cw) == 3: cw[1] = VietnameseToneNormalizer.normalize_word_typing(cw[1])
            words[index] = ''.join(cw)
        return ' '.join(words)
    
     
    @staticmethod
    def normalize_word_typing(word):
        if not VietnameseToneNormalizer.is_valid_vietnamese_word(word): return word
        chars, vowel_indexes = list(word), []
        qu_or_gi, tonal_mark = False, 0
        
        for index, char in enumerate(chars):
            if char not in VietnameseToneNormalizer.VOWELS_TO_IDS: continue
            row, col = VietnameseToneNormalizer.VOWELS_TO_IDS[char]
            if index > 0 and (row, chars[index - 1]) in [(9, 'q'), (5, 'g')]:
                chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][0]
                qu_or_gi = True
                
            if not qu_or_gi or index != 1: vowel_indexes.append(index)
            if col != 0:
                tonal_mark = col
                chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][0]
                
        if len(vowel_indexes) < 2:
            if qu_or_gi:
                index = 1 if len(chars) == 2 else 2
                if chars[index] in VietnameseToneNormalizer.VOWELS_TO_IDS:
                    row, _ = VietnameseToneNormalizer.VOWELS_TO_IDS[chars[index]]
                    chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][tonal_mark]
                else: chars[1] = VietnameseToneNormalizer.VOWELS_TABLE[5 if chars[1] == 'i' else 9][tonal_mark]
                return ''.join(chars)
            return word
        
        for index in vowel_indexes:
            row, _ = VietnameseToneNormalizer.VOWELS_TO_IDS[chars[index]]
            if row in [4, 8]: # ê, ơ
                chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][tonal_mark]
                return ''.join(chars)
            
        index = vowel_indexes[0 if len(vowel_indexes) == 2 and vowel_indexes[-1] == len(chars) - 1 else 1] 
        row, _ = VietnameseToneNormalizer.VOWELS_TO_IDS[chars[index]]
        chars[index] = VietnameseToneNormalizer.VOWELS_TABLE[row][tonal_mark]
        return ''.join(chars)
    
    
    @staticmethod
    def is_valid_vietnamese_word(word):
        vowel_indexes = -1 
        for index, char in enumerate(word):
            if char not in VietnameseToneNormalizer.VOWELS_TO_IDS: continue
            if vowel_indexes in [-1, index - 1]: vowel_indexes = index
            else: return False
        return True
    

from underthesea import word_tokenize

class VietnameseTextPreprocessor:
    def __init__(self, extra_teencodes=None, max_correction_length=512):
        self.extra_teencodes = extra_teencodes
        self._build_teencodes()
        
        self.max_correction_length = max_correction_length
        self.corrector = pipeline(
            'text2text-generation', model='bmd1905/vietnamese-correction-v2', 
            torch_dtype='bfloat16', device_map='auto', num_workers=os.cpu_count()
        )
        print('bmd1905/vietnamese-correction-v2 is loaded successfully.')
    
    def _build_teencodes(self):
        self.teencodes = {
            'ok': ['okie', 'okey', 'ôkê', 'oki', 'oke', 'okay', 'okê'], 
            'không': ['kg', 'not', 'k', 'kh', 'kô', 'hok', 'ko', 'khong'], 'không phải': ['kp'], 
            'cảm ơn': ['tks', 'thks', 'thanks', 'ths', 'thank'], 'hồi đó': ['hùi đó'], 'muốn': ['mún'],
            
            'rất tốt': ['perfect', '❤️', '😍'], 'dễ thương': ['cute'], 'yêu': ['iu'], 'thích': ['thik'], 
            'tốt': [
                'gud', 'good', 'gút', 'tot', 'nice',
                'hehe', 'hihi', 'haha', 'hjhj', 'thick', '^_^', ':)', '=)'
                '👍', '🎉', '😀', '😂', '🤗', '😙', '🙂'
            ], 
            'bình thường': ['bt', 'bthg'], 'hàg': ['hàng'], 
            'không tốt':  ['lol', 'cc', 'huhu', ':(', '😔', '😓'],
            'tệ': ['sad', 'por', 'poor', 'bad'], 'giả mạo': ['fake'], 
            'thôi': ['thui'],
            'quá': ['wa', 'wá', 'qá'], 'được': ['đx', 'dk', 'dc', 'đk', 'đc'], 
            'với': ['vs'], 'gì': ['j'], 'rồi': ['r'], 'mình': ['m', 'mik'], 
            'thời gian': ['time'], 'giờ': ['h'], 
        }
        if self.extra_teencodes: 
            for key, values in self.extra_teencodes.items():
                if any(len(value.split()) > 1 for value in values):
                    raise ValueError('The values for each key in extra_teencodes must be single words.')
                self.teencodes.setdefault(key, []).extend(values)
                
        self.teencodes = {word: key for key, values in self.teencodes.items() for word in values}
        teencode_url = 'https://raw.githubusercontent.com/htuann2712/ABSA-VLSP2018/refs/heads/main/teencode.txt'
        response = requests.get(teencode_url)
        
        if response.status_code == 200:
            text_data = StringIO(response.text)
            for pair in text_data:
                teencode, true_text = pair.split('\t')
                self.teencodes[teencode.strip()] = true_text.strip()
            self.teencodes = {k: self.teencodes[k] for k in sorted(self.teencodes)}
        else: print('Failed to fetch teencode.txt from', teencode_url)
    
    def normalize_teencodes(self, text):
        words = []
        for word in text.split():
            words.append(self.teencodes.get(word, word))
        return ' '.join(words)
    
    def correct_vietnamese_errors(self, texts):
        # https://huggingface.co/bmd1905/vietnamese-correction-v2
        predictions = self.corrector(texts, max_length=self.max_correction_length, truncation=True)
        return [prediction['generated_text'] for prediction in predictions]
    
    def word_segment(self, text):
        # Use underthesea for word segmentation
        return word_tokenize(text, format='text')
    
    def process_text(self, text, normalize_tone=True, segment=True):
        text = text.lower()
        if normalize_tone:
            text = VietnameseToneNormalizer.normalize_unicode(text)
            text = VietnameseToneNormalizer.normalize_sentence_typing(text)
        text = VietnameseTextCleaner.process_text(text)
        text = self.normalize_teencodes(text)
        return self.word_segment(text) if segment else text
    
    def process_batch(self, texts, correct_errors=True):
        if correct_errors:
            texts = [self.process_text(text, normalize_tone=True, segment=False) for text in texts]
            texts = self.correct_vietnamese_errors(texts)
            return [self.process_text(text, normalize_tone=False, segment=True) for text in texts]
        return [self.process_text(text, normalize_tone=True, segment=True) for text in texts]
    
from tqdm import tqdm

def preprocess_and_tokenize(text_data: pd.DataFrame, text_column: str, preprocessor, tokenizer, batch_size: int, max_length: int):
    print('[INFO] Preprocessing and tokenizing text data...')
    
    # Preprocess the text column using batch processing
    text_data[text_column] = text_data[text_column].apply(preprocessor.process_text)
    
    # Tokenize in batches
    tokenized_batches = []
    for i in tqdm(range(0, len(text_data), batch_size), desc='Tokenizing Batches'):
        batch = text_data[text_column].iloc[i:i+batch_size].tolist()
        tokenized_batch = tokenizer(batch, max_length=max_length, padding='max_length', truncation=True)
        tokenized_batches.extend(tokenized_batch['input_ids'])
    
    text_data['input_ids'] = tokenized_batches
    
    return text_data

import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
import pandas as pd

class ABSADataset(Dataset):
    def __init__(self, df: pd.DataFrame, tokenizer_name: str = 'vinai/phobert-base', max_length: int = 256):
        self.df = df
        self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        self.max_length = max_length
        
        # Xác định các aspect columns (loại bỏ cột Review)
        self.aspect_columns = [col for col in df.columns if col != 'Review']
        self.num_aspects = len(self.aspect_columns)
        
        # Map sentiment values to one-hot vectors
        self.sentiment_map = {
            0: [1, 0, 0, 0],  # None
            1: [0, 1, 0, 0],  # Positive
            2: [0, 0, 1, 0],  # Negative
            3: [0, 0, 0, 1]   # Neutral
        }

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]
        text = row['Review']
        
        # Tokenize text
        encoding = self.tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Get input_ids, attention_mask, and token_type_ids
        input_ids = encoding['input_ids'].squeeze(0)
        attention_mask = encoding['attention_mask'].squeeze(0)
        token_type_ids = encoding['token_type_ids'].squeeze(0)
        
        # Create labels tensor [num_aspects, 4] for sentiment classes
        labels = torch.zeros((self.num_aspects, 4))
        for i, aspect in enumerate(self.aspect_columns):
            sentiment_value = row[aspect]
            labels[i] = torch.tensor(self.sentiment_map[sentiment_value])
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'token_type_ids': token_type_ids,
            'labels': labels
        }

    @staticmethod
    def collate_fn(batch):
        return {
            'input_ids': torch.stack([x['input_ids'] for x in batch]),
            'attention_mask': torch.stack([x['attention_mask'] for x in batch]),
            'token_type_ids': torch.stack([x['token_type_ids'] for x in batch]),
            'labels': torch.stack([x['labels'] for x in batch])
        }

def create_dataloaders(df_train, df_val, df_test, batch_size=32, max_length=256):
    # Create datasets
    train_dataset = ABSADataset(df_train, max_length=max_length)
    val_dataset = ABSADataset(df_val, max_length=max_length)
    test_dataset = ABSADataset(df_test, max_length=max_length)
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=ABSADataset.collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=ABSADataset.collate_fn
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=ABSADataset.collate_fn
    )
    
    return train_loader, val_loader, test_loader

def train_epoch(model, train_loader, optimizer, criterion, device):
    model.train()
    total_loss = 0
    correct_predictions = 0
    total_predictions = 0
    
    progress_bar = tqdm(train_loader, desc='Training')
    
    for batch in progress_bar:
        # Move batch to device
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        token_type_ids = batch['token_type_ids'].to(device)
        labels = batch['labels'].to(device)
        
        # Forward pass
        optimizer.zero_grad()
        outputs = model(input_ids, attention_mask, token_type_ids)
        
        # Calculate loss
        # Reshape outputs and labels for loss calculation
        batch_size, num_aspects, num_classes = outputs.shape
        outputs = outputs.view(-1, num_classes)
        labels = labels.view(-1, num_classes)
        loss = criterion(outputs, labels)
        
        # Backward pass
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        
        # Calculate accuracy
        predictions = torch.argmax(outputs, dim=1)
        true_labels = torch.argmax(labels, dim=1)
        correct_predictions += (predictions == true_labels).sum().item()
        total_predictions += predictions.shape[0]
        
        # Update progress bar
        progress_bar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'acc': f'{correct_predictions/total_predictions:.4f}'
        })
    
    avg_loss = total_loss / len(train_loader)
    accuracy = correct_predictions / total_predictions
    
    return avg_loss, accuracy

def evaluate(model, val_loader, criterion, device):
    model.eval()
    total_loss = 0
    correct_predictions = 0
    total_predictions = 0
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc='Evaluating'):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            token_type_ids = batch['token_type_ids'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids, attention_mask, token_type_ids)
            
            # Reshape outputs and labels
            batch_size, num_aspects, num_classes = outputs.shape
            outputs = outputs.view(-1, num_classes)
            labels = labels.view(-1, num_classes)
            
            loss = criterion(outputs, labels)
            total_loss += loss.item()
            
            predictions = torch.argmax(outputs, dim=1)
            true_labels = torch.argmax(labels, dim=1)
            correct_predictions += (predictions == true_labels).sum().item()
            total_predictions += predictions.shape[0]
    
    avg_loss = total_loss / len(val_loader)
    accuracy = correct_predictions / total_predictions
    
    return avg_loss, accuracy

def train_model(model, train_loader, val_loader, optimizer, criterion, device, num_epochs=5):
    best_val_loss = float('inf')
    best_model_state = None
    
    for epoch in range(num_epochs):
        print(f'\nEpoch {epoch + 1}/{num_epochs}')
        print('-' * 20)
        
        # Training phase
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        print(f'Training Loss: {train_loss:.4f}, Accuracy: {train_acc:.4f}')
        
        # Validation phase
        val_loss, val_acc = evaluate(model, val_loader, criterion, device)
        print(f'Validation Loss: {val_loss:.4f}, Accuracy: {val_acc:.4f}')
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict()
            print('Best model saved!')
    
    return best_model_state, best_val_loss

import numpy as np
from sklearn.metrics import f1_score, precision_score, recall_score, classification_report
from typing import Dict, Tuple

def predict_and_evaluate(model: torch.nn.Module,
                        test_loader: torch.utils.data.DataLoader,
                        device: torch.device,
                        aspect_columns: list = None) -> Tuple[np.ndarray, Dict[str, float]]:
    """
    Predict và tính toán các metrics trên tập test
    
    Returns:
        predictions: Các dự đoán của model
        metrics: Dictionary chứa các metrics (f1, precision, recall) cho cả sentiment và aspect detection
    """
    model.eval()
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Predicting"):
            # Move batch to device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            token_type_ids = batch['token_type_ids'].to(device)
            labels = batch['labels']
            
            # Forward pass
            outputs = model(input_ids, attention_mask, token_type_ids)
            
            # Get predictions
            predictions = torch.argmax(outputs, dim=-1)
            
            # Move predictions and labels to CPU
            predictions = predictions.cpu().numpy()
            labels = torch.argmax(labels, dim=-1).numpy()
            
            all_predictions.append(predictions)
            all_labels.append(labels)
    
    # Convert to numpy arrays
    all_predictions = np.vstack(all_predictions)
    all_labels = np.vstack(all_labels)
    
    # Flatten for overall sentiment metrics
    flat_preds = all_predictions.flatten()
    flat_labels = all_labels.flatten()
    
    # Calculate sentiment metrics
    sentiment_metrics = {
        'f1_macro': f1_score(flat_labels, flat_preds, average='macro'),
        'f1_micro': f1_score(flat_labels, flat_preds, average='micro'),
        'f1_weighted': f1_score(flat_labels, flat_preds, average='weighted'),
        'precision_macro': precision_score(flat_labels, flat_preds, average='macro'),
        'precision_micro': precision_score(flat_labels, flat_preds, average='micro'),
        'precision_weighted': precision_score(flat_labels, flat_preds, average='weighted'),
        'recall_macro': recall_score(flat_labels, flat_preds, average='macro'),
        'recall_micro': recall_score(flat_labels, flat_preds, average='micro'),
        'recall_weighted': recall_score(flat_labels, flat_preds, average='weighted'),
    }
    
    # Print detailed sentiment classification report
    print("\nSentiment Classification Report:")
    print(classification_report(flat_labels, flat_preds))
    print("\nSentiment Classification Metrics:")
    print(f"Macro F1-Score: {sentiment_metrics['f1_macro']:.4f}")
    print(f"Micro F1-Score: {sentiment_metrics['f1_micro']:.4f}")
    print(f"Weighted F1-Score: {sentiment_metrics['f1_weighted']:.4f}")
    print("\nPrecision Scores:")
    print(f"Macro: {sentiment_metrics['precision_macro']:.4f}")
    print(f"Micro: {sentiment_metrics['precision_micro']:.4f}")
    print(f"Weighted: {sentiment_metrics['precision_weighted']:.4f}")
    print("\nRecall Scores:")
    print(f"Macro: {sentiment_metrics['recall_macro']:.4f}")
    print(f"Micro: {sentiment_metrics['recall_micro']:.4f}")
    print(f"Weighted: {sentiment_metrics['recall_weighted']:.4f}")
    
    # Calculate aspect detection metrics
    if aspect_columns:
        # Convert to binary aspect detection (0: None, 1: Any sentiment)
        pred_aspects = (all_predictions != 0).astype(int)
        true_aspects = (all_labels != 0).astype(int)
        
        # Prepare data for classification report
        y_true = []
        y_pred = []
        
        # For each sample and each aspect
        for i in range(true_aspects.shape[0]):
            for j in range(true_aspects.shape[1]):
                # Only include aspects that are actually present in the true data
                if true_aspects[i, j] == 1:
                    y_true.append(j)  # The aspect index
                    if pred_aspects[i, j] == 1:
                        y_pred.append(j)  # Correctly predicted
                    else:
                        y_pred.append(-1)  # Missed aspect
        
        # Print aspect detection classification report
        print("\nAspect Detection Classification Report:")
        print(classification_report(
            y_true, 
            y_pred,
            labels=range(len(aspect_columns)),
            target_names=aspect_columns
        ))
        
        # Calculate overall aspect metrics
        aspect_metrics = {
            'aspect_f1_macro': f1_score(true_aspects.flatten(), pred_aspects.flatten(), average='macro'),
            'aspect_f1_micro': f1_score(true_aspects.flatten(), pred_aspects.flatten(), average='micro'),
            'aspect_f1_weighted': f1_score(true_aspects.flatten(), pred_aspects.flatten(), average='weighted'),
            'aspect_precision_macro': precision_score(true_aspects.flatten(), pred_aspects.flatten(), average='macro'),
            'aspect_precision_micro': precision_score(true_aspects.flatten(), pred_aspects.flatten(), average='micro'),
            'aspect_precision_weighted': precision_score(true_aspects.flatten(), pred_aspects.flatten(), average='weighted'),
            'aspect_recall_macro': recall_score(true_aspects.flatten(), pred_aspects.flatten(), average='macro'),
            'aspect_recall_micro': recall_score(true_aspects.flatten(), pred_aspects.flatten(), average='micro'),
            'aspect_recall_weighted': recall_score(true_aspects.flatten(), pred_aspects.flatten(), average='weighted'),
        }
        
        print("\nOverall Aspect Detection Metrics:")
        print(f"Macro F1-Score: {aspect_metrics['aspect_f1_macro']:.4f}")
        print(f"Micro F1-Score: {aspect_metrics['aspect_f1_micro']:.4f}")
        print(f"Weighted F1-Score: {aspect_metrics['aspect_f1_weighted']:.4f}")
        print("\nPrecision Scores:")
        print(f"Macro: {aspect_metrics['aspect_precision_macro']:.4f}")
        print(f"Micro: {aspect_metrics['aspect_precision_micro']:.4f}")
        print(f"Weighted: {aspect_metrics['aspect_precision_weighted']:.4f}")
        print("\nRecall Scores:")
        print(f"Macro: {aspect_metrics['aspect_recall_macro']:.4f}")
        print(f"Micro: {aspect_metrics['aspect_recall_micro']:.4f}")
        print(f"Weighted: {aspect_metrics['aspect_recall_weighted']:.4f}")
        
        # Merge metrics
        metrics = {**sentiment_metrics, **aspect_metrics}
    else:
        metrics = sentiment_metrics
    
    return all_predictions, metrics

def predict_aspects_and_sentiments(model: torch.nn.Module,
                                 test_loader: torch.utils.data.DataLoader,
                                 device: torch.device,
                                 aspect_columns: list) -> pd.DataFrame:
    """
    Predict aspects và sentiments cho từng review và trả về DataFrame kết quả
    """
    model.eval()
    all_predictions = []
    all_reviews = []
    
    sentiment_map = {0: 'None', 1: 'Positive', 2: 'Negative', 3: 'Neutral'}
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Generating predictions"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            token_type_ids = batch['token_type_ids'].to(device)
            
            # Get model predictions
            outputs = model(input_ids, attention_mask, token_type_ids)
            predictions = torch.argmax(outputs, dim=-1).cpu().numpy()
            
            all_predictions.append(predictions)
            
            # Get original reviews if available in batch
            if 'review_texts' in batch:
                all_reviews.extend(batch['review_texts'])
    
    # Combine all predictions
    all_predictions = np.vstack(all_predictions)
    
    # Create DataFrame with predictions
    results = []
    for i in range(len(all_predictions)):
        row = {}
        for j, aspect in enumerate(aspect_columns):
            row[aspect] = sentiment_map[all_predictions[i][j]]
        if all_reviews:
            row['Review'] = all_reviews[i]
        results.append(row)
    
    return pd.DataFrame(results)

def save_predictions(predictions_df: pd.DataFrame,
                    metrics: Dict[str, float],
                    output_path: str = 'predictions',
                    save_csv: bool = True,
                    save_metrics: bool = True):
    """
    Lưu kết quả predictions và metrics
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)
    
    # Save predictions to CSV
    if save_csv:
        csv_path = os.path.join(output_path, 'predictions.csv')
        predictions_df.to_csv(csv_path, index=False, encoding='utf-8')
        print(f"Predictions saved to: {csv_path}")
    
    # Save metrics to text file
    if save_metrics:
        metrics_path = os.path.join(output_path, 'metrics.txt')
        with open(metrics_path, 'w') as f:
            f.write("Model Evaluation Metrics:\n")
            f.write("=" * 50 + "\n\n")
            for metric_name, value in metrics.items():
                f.write(f"{metric_name}: {value:.4f}\n")
        print(f"Metrics saved to: {metrics_path}")
